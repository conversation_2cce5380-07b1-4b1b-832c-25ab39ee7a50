import SimpleEditor from '@/components/SimpleEditor';
import { templates } from '@/data/templates';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Grid, Type, Hash, Circle, Cake, Users, Heart as Wedding, MapPin, Camera, UserCheck, Calendar } from 'lucide-react';

const categoryIcons = {
  grid: Grid,
  heart: Heart,
  letter: Type,
  number: Hash,
  shape: Circle,
  birthday: Cake,
  family: Users,
  wedding: Wedding,
  travel: MapPin,
  memory: Camera,
  friends: UserCheck,
  anniversary: Calendar
};

const categoryDescriptions = {
  grid: 'Create perfect grid layouts for your photo collections',
  heart: 'Design romantic heart-shaped photo collages',
  letter: 'Spell out messages with letter-shaped photo arrangements',
  number: 'Celebrate special dates with number-shaped collages',
  shape: 'Explore creative geometric shapes for your photos',
  birthday: 'Celebrate birthdays with fun cake and balloon layouts',
  family: 'Beautiful family tree and mom-dedicated collages',
  wedding: 'Romantic wedding and engagement photo arrangements',
  travel: 'Document your adventures with travel-themed layouts',
  memory: 'Preserve precious memories with timeline collages',
  friends: 'Celebrate friendship with special group layouts',
  anniversary: 'Mark special milestones with anniversary templates'
};

export default function Home() {
  // Group templates by category
  const templatesByCategory = templates.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, typeof templates>);

  return (
    <main className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
            Photo Collage Maker
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
            Create beautiful photo collages with our easy-to-use templates.
            Choose from heart shapes, grids, letters, numbers and custom templates!
          </p>
        </div>

        {/* Template Categories */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold text-center mb-8">Choose Your Template Style</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(templatesByCategory).map(([category, categoryTemplates]) => {
              const IconComponent = categoryIcons[category as keyof typeof categoryIcons];
              return (
                <Link key={category} href={`/templates/${category}`} className="block">
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer group">
                    <CardContent className="p-6">
                      <div className="flex items-center mb-4">
                        {IconComponent && <IconComponent className="w-8 h-8 text-blue-600 mr-3" />}
                        <h3 className="text-xl font-semibold capitalize">{category} Templates</h3>
                      </div>
                      <p className="text-gray-600 mb-4">
                        {categoryDescriptions[category as keyof typeof categoryDescriptions]}
                      </p>
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary">
                          {categoryTemplates.length} templates
                        </Badge>
                        <span className="text-blue-600 group-hover:text-blue-800 font-medium">
                          Explore →
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Quick Start Section */}
        <div className="text-center mb-16">
          <h2 className="text-2xl font-bold mb-4">Or Start with Our Most Popular Template</h2>
          <SimpleEditor initialTemplateId="grid-4x4" />
        </div>

        {/* SEO Content Section */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-3xl font-bold text-center mb-8">
              Create Beautiful Photo Collages Online for Free
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-xl font-semibold mb-4">Why Choose Our Photo Collage Maker?</h3>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">✓</span>
                    <span><strong>50+ Professional Templates:</strong> Choose from heart shapes, grids, letters, numbers, and creative layouts</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">✓</span>
                    <span><strong>Completely Free:</strong> No watermarks, no registration required, unlimited downloads</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">✓</span>
                    <span><strong>Easy to Use:</strong> Drag and drop interface, perfect for beginners and professionals</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">✓</span>
                    <span><strong>High Quality:</strong> Download your collages in high-resolution PNG format</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Perfect For</h3>
                <ul className="space-y-3 text-gray-600">
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    <span>Social media posts (Instagram, Facebook, Twitter)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    <span>Special occasions (birthdays, anniversaries, holidays)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    <span>Memory books and photo albums</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    <span>Gifts and personalized cards</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-2">•</span>
                    <span>Business presentations and marketing materials</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="border-t pt-8">
              <h3 className="text-xl font-semibold mb-4">How to Create a Photo Collage</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">1</span>
                  </div>
                  <h4 className="font-medium mb-2">Choose Template</h4>
                  <p className="text-sm text-gray-600">Select from our collection of 50+ templates</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">2</span>
                  </div>
                  <h4 className="font-medium mb-2">Upload Photos</h4>
                  <p className="text-sm text-gray-600">Drag and drop or click to upload your images</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">3</span>
                  </div>
                  <h4 className="font-medium mb-2">Customize</h4>
                  <p className="text-sm text-gray-600">Arrange and adjust your photos perfectly</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 font-bold">4</span>
                  </div>
                  <h4 className="font-medium mb-2">Download</h4>
                  <p className="text-sm text-gray-600">Save your collage in high quality</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
