import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { templates } from '@/data/templates';
import SimpleEditor from '@/components/SimpleEditor';
import { Heart, Grid, Type, Hash, Circle, Cake, Users, MapPin, Camera, UserCheck, Calendar } from 'lucide-react';
import StructuredData from '@/components/StructuredData';

interface PageProps {
  params: {
    category: string;
  };
}

const categoryIcons = {
  grid: Grid,
  heart: Heart,
  letter: Type,
  number: Hash,
  shape: Circle,
  birthday: Cake,
  family: Users,
  wedding: Heart,
  travel: MapPin,
  memory: Camera,
  friends: UserCheck,
  anniversary: Calendar
};

const categoryMetadata = {
  grid: {
    title: 'Grid Photo Collage Templates - Create Perfect Grid Layouts',
    description: 'Create stunning grid photo collages with our free online editor. Choose from 2x2, 3x3, 4x4 and custom grid layouts. Perfect for Instagram, social media, and photo displays.',
    keywords: ['grid photo collage', 'photo grid maker', '4x4 photo grid', '3x3 collage', 'instagram grid', 'photo layout grid'],
    h1: 'Grid Photo Collage Templates',
    subtitle: 'Create perfect grid layouts for your photo collections with our easy-to-use templates'
  },
  heart: {
    title: 'Heart Photo Collage Templates - Romantic Love Collages',
    description: 'Design beautiful heart-shaped photo collages for Valentine\'s Day, anniversaries, and romantic occasions. Free heart collage maker with multiple heart templates.',
    keywords: ['heart photo collage', 'love collage maker', 'valentine collage', 'romantic photo collage', 'heart shaped photos'],
    h1: 'Heart Photo Collage Templates',
    subtitle: 'Design romantic heart-shaped photo collages for special occasions and loved ones'
  },
  letter: {
    title: 'Letter Photo Collage Templates - Spell with Photos',
    description: 'Create letter-shaped photo collages to spell names, words, and messages. Perfect for birthdays, graduations, and personalized gifts. Free letter collage maker.',
    keywords: ['letter photo collage', 'alphabet photo collage', 'name photo collage', 'letter shaped photos', 'personalized photo letters'],
    h1: 'Letter Photo Collage Templates',
    subtitle: 'Spell out messages and names with creative letter-shaped photo arrangements'
  },
  number: {
    title: 'Number Photo Collage Templates - Celebrate Special Dates',
    description: 'Create number-shaped photo collages for birthdays, anniversaries, and milestone celebrations. Free number collage templates for ages, years, and special dates.',
    keywords: ['number photo collage', 'birthday number collage', 'anniversary collage', 'age photo collage', 'milestone photo collage'],
    h1: 'Number Photo Collage Templates',
    subtitle: 'Celebrate special dates and milestones with number-shaped photo collages'
  },
  shape: {
    title: 'Creative Shape Photo Collage Templates - Unique Layouts',
    description: 'Explore creative geometric and custom shape photo collages. Unique templates for artistic photo arrangements and creative displays.',
    keywords: ['shape photo collage', 'geometric photo collage', 'creative photo layouts', 'artistic photo collage', 'custom shape collage'],
    h1: 'Creative Shape Photo Collage Templates',
    subtitle: 'Explore unique geometric shapes and creative layouts for your photo collections'
  },
  birthday: {
    title: 'Birthday Photo Collage Templates - Celebrate Special Days',
    description: 'Create fun birthday photo collages with cake and balloon templates. Perfect for birthday parties, celebrations, and memory books.',
    keywords: ['birthday photo collage', 'birthday cake collage', 'birthday party photos', 'celebration collage', 'birthday memories'],
    h1: 'Birthday Photo Collage Templates',
    subtitle: 'Celebrate birthdays with fun cake and balloon-themed photo arrangements'
  },
  family: {
    title: 'Family Photo Collage Templates - Cherish Family Moments',
    description: 'Beautiful family photo collages including family tree layouts and mom-dedicated templates. Perfect for family reunions and special occasions.',
    keywords: ['family photo collage', 'family tree collage', 'mom collage', 'family memories', 'family photo arrangement'],
    h1: 'Family Photo Collage Templates',
    subtitle: 'Cherish family moments with beautiful tree layouts and dedicated family templates'
  },
  wedding: {
    title: 'Wedding Photo Collage Templates - Romantic Wedding Memories',
    description: 'Create stunning wedding photo collages with romantic ring and heart layouts. Perfect for wedding albums and anniversary celebrations.',
    keywords: ['wedding photo collage', 'wedding ring collage', 'romantic wedding photos', 'wedding memories', 'bridal collage'],
    h1: 'Wedding Photo Collage Templates',
    subtitle: 'Preserve your wedding memories with romantic and elegant photo arrangements'
  },
  travel: {
    title: 'Travel Photo Collage Templates - Document Your Adventures',
    description: 'Create amazing travel photo collages with map-style layouts. Perfect for vacation memories and travel journals.',
    keywords: ['travel photo collage', 'vacation collage', 'travel map photos', 'adventure collage', 'travel memories'],
    h1: 'Travel Photo Collage Templates',
    subtitle: 'Document your adventures and travels with map-inspired photo layouts'
  },
  memory: {
    title: 'Memory Photo Collage Templates - Preserve Precious Moments',
    description: 'Create timeline-style memory collages to preserve your most precious moments. Perfect for life events and milestone celebrations.',
    keywords: ['memory photo collage', 'timeline collage', 'life memories', 'precious moments', 'memory lane photos'],
    h1: 'Memory Photo Collage Templates',
    subtitle: 'Preserve precious memories with timeline and memory lane photo arrangements'
  },
  friends: {
    title: 'Friends Photo Collage Templates - Celebrate Friendship',
    description: 'Create special friendship photo collages with best friend layouts. Perfect for celebrating friendships and group memories.',
    keywords: ['friends photo collage', 'best friend collage', 'friendship photos', 'group photo collage', 'friend memories'],
    h1: 'Friends Photo Collage Templates',
    subtitle: 'Celebrate friendship with special group layouts and best friend templates'
  },
  anniversary: {
    title: 'Anniversary Photo Collage Templates - Milestone Celebrations',
    description: 'Create beautiful anniversary photo collages for milestone celebrations. Perfect for wedding anniversaries and special relationship milestones.',
    keywords: ['anniversary photo collage', '25th anniversary collage', 'milestone celebration', 'anniversary memories', 'relationship milestones'],
    h1: 'Anniversary Photo Collage Templates',
    subtitle: 'Mark special relationship milestones with beautiful anniversary photo arrangements'
  }
};

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { category } = params;
  const metadata = categoryMetadata[category as keyof typeof categoryMetadata];
  
  if (!metadata) {
    return {
      title: 'Template Not Found',
      description: 'The requested template category was not found.'
    };
  }

  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords,
    openGraph: {
      title: metadata.title,
      description: metadata.description,
      type: 'website',
      url: `https://photocollagemakerpro.com/templates/${category}`,
      images: [
        {
          url: `https://photocollagemakerpro.com/og-${category}.jpg`,
          width: 1200,
          height: 630,
          alt: metadata.h1
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.title,
      description: metadata.description,
      images: [`https://photocollagemakerpro.com/og-${category}.jpg`]
    },
    alternates: {
      canonical: `https://photocollagemakerpro.com/templates/${category}`
    }
  };
}

export async function generateStaticParams() {
  const categories = [...new Set(templates.map(template => template.category))];
  return categories.map((category) => ({
    category: category,
  }));
}

export default function CategoryPage({ params }: PageProps) {
  const { category } = params;
  const categoryTemplates = templates.filter(template => template.category === category);
  const metadata = categoryMetadata[category as keyof typeof categoryMetadata];
  
  if (!metadata || categoryTemplates.length === 0) {
    notFound();
  }

  const IconComponent = categoryIcons[category as keyof typeof categoryIcons];
  const firstTemplate = categoryTemplates[0];

  // Structured data for the category page
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: metadata.h1,
    description: metadata.description,
    url: `https://photocollagemakerpro.com/templates/${category}`,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: categoryTemplates.length,
      itemListElement: categoryTemplates.map((template, index) => ({
        '@type': 'CreativeWork',
        position: index + 1,
        name: template.name,
        description: template.description,
        category: template.category,
        url: `https://photocollagemakerpro.com/templates/${category}/${template.id}`
      }))
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: 'https://photocollagemakerpro.com'
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Templates',
          item: 'https://photocollagemakerpro.com/templates'
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: metadata.h1,
          item: `https://photocollagemakerpro.com/templates/${category}`
        }
      ]
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            {IconComponent && <IconComponent className="w-12 h-12 text-blue-600 mr-4" />}
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800">
              {metadata.h1}
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {metadata.subtitle}
          </p>
        </div>

        {/* Template Editor */}
        <SimpleEditor initialTemplateId={firstTemplate.id} />
        
        {/* Available Templates */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold text-center mb-8">
            Available {metadata.h1.split(' ')[0]} Templates ({categoryTemplates.length})
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {categoryTemplates.map((template) => (
              <Link
                key={template.id}
                href={`/templates/${category}/${template.id}`}
                className="block"
              >
                <div className="bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                  <div className="text-center">
                    <div className="w-full h-20 bg-gray-100 rounded mb-2 flex items-center justify-center">
                      <span className="text-xs text-gray-500">{template.name}</span>
                    </div>
                    <p className="text-sm font-medium">{template.name}</p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-2xl font-bold mb-6">
              How to Create {metadata.h1.split(' ')[0]} Photo Collages
            </h2>
            <div className="prose max-w-none">
              <ol className="list-decimal list-inside space-y-3">
                <li><strong>Choose Your Template:</strong> Select from our collection of {categoryTemplates.length} {category} templates above</li>
                <li><strong>Upload Photos:</strong> Click the upload button or drag and drop your images (JPG, PNG, WebP supported)</li>
                <li><strong>Arrange Photos:</strong> Drag photos into the template slots and adjust positioning as needed</li>
                <li><strong>Customize:</strong> Fine-tune photo placement, rotation, and scaling for the perfect look</li>
                <li><strong>Download:</strong> Save your finished collage as a high-quality PNG image</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
