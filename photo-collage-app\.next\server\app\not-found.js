/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CFooter.tsx&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CNavigation.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CFooter.tsx&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CNavigation.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(ssr)/./src/components/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMmRpc3BsYXklMjIlM0ElMjJzd2FwJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1DJTNBJTVDV29ya1NwYWNlJTVDcGhvdG9Db2xsYWdlJTVDcGhvdG8tY29sbGFnZS1hcHAlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNXb3JrU3BhY2UlNUNwaG90b0NvbGxhZ2UlNUNwaG90by1jb2xsYWdlLWFwcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNGb290ZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDc3JjJTVDY29tcG9uZW50cyU1Q05hdmlnYXRpb24udHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBZ0g7QUFDaEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9waG90by1jb2xsYWdlLWFwcC8/ZjBkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcRm9vdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXHBob3RvQ29sbGFnZVxcXFxwaG90by1jb2xsYWdlLWFwcFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxOYXZpZ2F0aW9uLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CFooter.tsx&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CNavigation.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cake.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Type,UserCheck,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const categoryLinks = [\n        {\n            href: \"/templates/grid\",\n            label: \"Grid Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            href: \"/templates/heart\",\n            label: \"Heart Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            href: \"/templates/letter\",\n            label: \"Letter Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: \"/templates/number\",\n            label: \"Number Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: \"/templates/shape\",\n            label: \"Shape Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: \"/templates/birthday\",\n            label: \"Birthday Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: \"/templates/family\",\n            label: \"Family Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            href: \"/templates/wedding\",\n            label: \"Wedding Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            href: \"/templates/travel\",\n            label: \"Travel Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            href: \"/templates/memory\",\n            label: \"Memory Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            href: \"/templates/friends\",\n            label: \"Friends Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            href: \"/templates/anniversary\",\n            label: \"Anniversary Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Type_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"Photo Collage Online\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-gray-400 text-sm\",\n                                    children: \"Create beautiful photo collages online for free. Choose from 50+ templates and download high-quality results instantly.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Templates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"All Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        categoryLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: link.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.href, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"FAQ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Browse Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Create Collage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Popular Templates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/grid/grid-4x4\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"4\\xd74 Grid\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/heart/heart-classic\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Classic Heart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/letter/letter-a\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Letter A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/number/number-1\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Number 1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-12 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Photo Collage Maker Pro. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/sitemap.xml\",\n                                                className: \"text-gray-400 hover:text-white transition-colors text-sm\",\n                                                children: \"Sitemap\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-gray-400 hover:text-white transition-colors text-sm\",\n                                                children: \"Help\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cake.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Cake,Calendar,Camera,Circle,Grid,Hash,Heart,MapPin,Menu,Type,UserCheck,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Navigation = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const categoryLinks = [\n        {\n            href: \"/templates/grid\",\n            label: \"Grid Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: \"/templates/heart\",\n            label: \"Heart Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: \"/templates/letter\",\n            label: \"Letter Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: \"/templates/number\",\n            label: \"Number Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: \"/templates/shape\",\n            label: \"Shape Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            href: \"/templates/birthday\",\n            label: \"Birthday Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            href: \"/templates/family\",\n            label: \"Family Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            href: \"/templates/wedding\",\n            label: \"Wedding Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: \"/templates/travel\",\n            label: \"Travel Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        },\n        {\n            href: \"/templates/memory\",\n            label: \"Memory Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            href: \"/templates/friends\",\n            label: \"Friends Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            href: \"/templates/anniversary\",\n            label: \"Anniversary Templates\",\n            icon: _barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-blue-600\",\n                                children: \"Photo Collage Online\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/templates\",\n                                            className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-2\",\n                                                children: categoryLinks.map((link)=>{\n                                                    const IconComponent = link.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: link.href,\n                                                        className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 55,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            link.label\n                                                        ]\n                                                    }, link.href, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 50,\n                                                        columnNumber: 23\n                                                    }, undefined);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/faq\",\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                    children: \"FAQ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        children: \"Create Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 29\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cake_Calendar_Camera_Circle_Grid_Hash_Heart_MapPin_Menu_Type_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 57\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"block px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/templates\",\n                                className: \"block px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"All Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 15\n                            }, undefined),\n                            categoryLinks.map((link)=>{\n                                const IconComponent = link.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: link.href,\n                                    className: \"flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        link.label\n                                    ]\n                                }, link.href, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 19\n                                }, undefined);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/faq\",\n                                className: \"block px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    asChild: true,\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Create Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRlZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OTQwYzc3YzkwNTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Photo Collage Online - Create Beautiful Collages Free | 2024\",\n        template: \"%s | Photo Collage Online\"\n    },\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 50+ templates including heart shapes, grids, letters, numbers and custom layouts. Upload photos and download high-quality collages instantly. Perfect for social media, gifts, and memories.\",\n    keywords: [\n        \"photo collage maker\",\n        \"free collage maker online\",\n        \"photo collage creator\",\n        \"collage editor\",\n        \"photo montage maker\",\n        \"picture collage online\",\n        \"heart photo collage\",\n        \"grid photo collage\",\n        \"letter photo collage\",\n        \"number photo collage\",\n        \"instagram collage maker\",\n        \"social media collage\",\n        \"photo layout maker\",\n        \"collage template free\",\n        \"online photo editor\",\n        \"photo arrangement tool\"\n    ],\n    authors: [\n        {\n            name: \"Photo Collage Maker Team\"\n        }\n    ],\n    creator: \"Photo Collage Maker\",\n    publisher: \"Photo Collage Maker\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://photocollagemakerpro.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Photo Collage Online - Create Beautiful Collages Free | 2024\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 100+ templates including heart shapes, grids, letters, numbers and custom layouts. Perfect for social media, gifts, and memories.\",\n        siteName: \"Photo Collage Online\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Photo Collage Maker Pro - Create Beautiful Collages Online Free\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free | 2024\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 50+ templates including heart shapes, grids, letters, numbers and custom layouts.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@photocollagemakerpro\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\",\n        yandex: \"your-yandex-verification-code\",\n        yahoo: \"your-yahoo-verification-code\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 5,\n    userScalable: true,\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"#ffffff\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"#000000\"\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                name: \"Photo Collage Online\",\n                                description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 100+ templates including heart shapes, grids, letters, numbers and custom layouts. Perfect for social media, gifts, and memories.\",\n                                url: \"https://photocollagemakerpro.com\",\n                                applicationCategory: \"DesignApplication\",\n                                operatingSystem: \"Web Browser\",\n                                offers: {\n                                    \"@type\": \"Offer\",\n                                    price: \"0\",\n                                    priceCurrency: \"USD\",\n                                    availability: \"https://schema.org/InStock\"\n                                },\n                                featureList: [\n                                    \"Free photo collage maker\",\n                                    \"100+ professional templates\",\n                                    \"Heart-shaped collages\",\n                                    \"Grid layouts (2x2, 3x3, 4x4)\",\n                                    \"Letter and number templates\",\n                                    \"Birthday and celebration templates\",\n                                    \"Family and wedding layouts\",\n                                    \"Travel and memory collages\",\n                                    \"Drag and drop interface\",\n                                    \"High-quality PNG downloads\",\n                                    \"No registration required\",\n                                    \"Mobile-friendly editor\",\n                                    \"Social media optimized sizes\"\n                                ],\n                                screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n                                softwareVersion: \"1.0.0\",\n                                aggregateRating: {\n                                    \"@type\": \"AggregateRating\",\n                                    ratingValue: \"4.8\",\n                                    ratingCount: \"1250\",\n                                    bestRating: \"5\",\n                                    worstRating: \"1\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\photoCollage\photo-collage-app\src\components\Footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\photoCollage\photo-collage-app\src\components\Navigation.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();