import { CollageTemplate } from '@/types/template';

export const templates: CollageTemplate[] = [
  // Grid Layout Templates
  {
    id: 'grid-2x2',
    name: '2x2 Grid',
    description: 'Simple 4-photo grid layout',
    category: 'grid',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 600,
    backgroundColor: '#ffffff',
    slots: [
      { id: 'slot-1', x: 5, y: 5, width: 40, height: 40, shape: 'rectangle' },
      { id: 'slot-2', x: 55, y: 5, width: 40, height: 40, shape: 'rectangle' },
      { id: 'slot-3', x: 5, y: 55, width: 40, height: 40, shape: 'rectangle' },
      { id: 'slot-4', x: 55, y: 55, width: 40, height: 40, shape: 'rectangle' },
    ]
  },
  {
    id: 'grid-3x3',
    name: '3x3 Grid',
    description: 'Perfect 9-photo grid layout',
    category: 'grid',
    thumbnail: '',
    canvasWidth: 700,
    canvasHeight: 700,
    backgroundColor: '#ffffff',
    slots: [
      // Row 1
      { id: 'slot-1', x: 5, y: 5, width: 26, height: 26, shape: 'rectangle' },
      { id: 'slot-2', x: 37, y: 5, width: 26, height: 26, shape: 'rectangle' },
      { id: 'slot-3', x: 69, y: 5, width: 26, height: 26, shape: 'rectangle' },
      // Row 2
      { id: 'slot-4', x: 5, y: 37, width: 26, height: 26, shape: 'rectangle' },
      { id: 'slot-5', x: 37, y: 37, width: 26, height: 26, shape: 'rectangle' },
      { id: 'slot-6', x: 69, y: 37, width: 26, height: 26, shape: 'rectangle' },
      // Row 3
      { id: 'slot-7', x: 5, y: 69, width: 26, height: 26, shape: 'rectangle' },
      { id: 'slot-8', x: 37, y: 69, width: 26, height: 26, shape: 'rectangle' },
      { id: 'slot-9', x: 69, y: 69, width: 26, height: 26, shape: 'rectangle' },
    ]
  },
  {
    id: 'grid-4x4',
    name: '4x4 Grid',
    description: 'Classic 16-photo grid layout',
    category: 'grid',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 800,
    backgroundColor: '#ffffff',
    slots: [
      // Row 1
      { id: 'slot-1', x: 2, y: 2, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-2', x: 27, y: 2, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-3', x: 52, y: 2, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-4', x: 77, y: 2, width: 21, height: 21, shape: 'rectangle' },
      // Row 2
      { id: 'slot-5', x: 2, y: 27, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-6', x: 27, y: 27, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-7', x: 52, y: 27, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-8', x: 77, y: 27, width: 21, height: 21, shape: 'rectangle' },
      // Row 3
      { id: 'slot-9', x: 2, y: 52, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-10', x: 27, y: 52, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-11', x: 52, y: 52, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-12', x: 77, y: 52, width: 21, height: 21, shape: 'rectangle' },
      // Row 4
      { id: 'slot-13', x: 2, y: 77, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-14', x: 27, y: 77, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-15', x: 52, y: 77, width: 21, height: 21, shape: 'rectangle' },
      { id: 'slot-16', x: 77, y: 77, width: 21, height: 21, shape: 'rectangle' },
    ]
  },

  // Heart Shape Template
  {
    id: 'heart-shape',
    name: 'Heart Collage',
    description: 'Romantic heart-shaped photo arrangement',
    category: 'heart',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 700,
    backgroundColor: '#ffe6f2',
    slots: [
      // Top left curve
      { id: 'heart-1', x: 15, y: 15, width: 15, height: 15, shape: 'circle' },
      { id: 'heart-2', x: 32, y: 10, width: 12, height: 12, shape: 'circle' },
      { id: 'heart-3', x: 10, y: 32, width: 12, height: 12, shape: 'circle' },
      
      // Top right curve
      { id: 'heart-4', x: 55, y: 15, width: 15, height: 15, shape: 'circle' },
      { id: 'heart-5', x: 72, y: 10, width: 12, height: 12, shape: 'circle' },
      { id: 'heart-6', x: 78, y: 32, width: 12, height: 12, shape: 'circle' },
      
      // Center area
      { id: 'heart-7', x: 35, y: 35, width: 30, height: 20, shape: 'rectangle' },
      
      // Lower sections
      { id: 'heart-8', x: 25, y: 58, width: 18, height: 15, shape: 'rectangle' },
      { id: 'heart-9', x: 57, y: 58, width: 18, height: 15, shape: 'rectangle' },
      { id: 'heart-10', x: 42, y: 75, width: 16, height: 12, shape: 'rectangle' },
    ]
  },

  // Letter 'A' Template
  {
    id: 'letter-a',
    name: 'Letter A',
    description: 'Letter A shaped photo collage',
    category: 'letter',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 800,
    backgroundColor: '#f0f8ff',
    slots: [
      // Top point
      { id: 'a-top', x: 45, y: 5, width: 10, height: 15, shape: 'rectangle' },
      
      // Upper left diagonal
      { id: 'a-ul1', x: 35, y: 22, width: 12, height: 15, shape: 'rectangle', rotation: -15 },
      { id: 'a-ul2', x: 25, y: 38, width: 12, height: 15, shape: 'rectangle', rotation: -15 },
      
      // Upper right diagonal
      { id: 'a-ur1', x: 53, y: 22, width: 12, height: 15, shape: 'rectangle', rotation: 15 },
      { id: 'a-ur2', x: 63, y: 38, width: 12, height: 15, shape: 'rectangle', rotation: 15 },
      
      // Cross bar
      { id: 'a-cross1', x: 35, y: 50, width: 12, height: 8, shape: 'rectangle' },
      { id: 'a-cross2', x: 53, y: 50, width: 12, height: 8, shape: 'rectangle' },
      
      // Lower left leg
      { id: 'a-ll1', x: 15, y: 65, width: 12, height: 15, shape: 'rectangle' },
      { id: 'a-ll2', x: 15, y: 82, width: 12, height: 15, shape: 'rectangle' },
      
      // Lower right leg
      { id: 'a-lr1', x: 73, y: 65, width: 12, height: 15, shape: 'rectangle' },
      { id: 'a-lr2', x: 73, y: 82, width: 12, height: 15, shape: 'rectangle' },
    ]
  },

  // Number '1' Template
  {
    id: 'number-1',
    name: 'Number 1',
    description: 'Number 1 shaped photo collage',
    category: 'number',
    thumbnail: '',
    canvasWidth: 400,
    canvasHeight: 800,
    backgroundColor: '#fff5ee',
    slots: [
      // Top diagonal
      { id: 'num1-top', x: 25, y: 5, width: 15, height: 12, shape: 'rectangle', rotation: 45 },
      
      // Main vertical line
      { id: 'num1-1', x: 40, y: 15, width: 20, height: 15, shape: 'rectangle' },
      { id: 'num1-2', x: 40, y: 32, width: 20, height: 15, shape: 'rectangle' },
      { id: 'num1-3', x: 40, y: 49, width: 20, height: 15, shape: 'rectangle' },
      { id: 'num1-4', x: 40, y: 66, width: 20, height: 15, shape: 'rectangle' },
      
      // Bottom base
      { id: 'num1-base1', x: 20, y: 83, width: 20, height: 12, shape: 'rectangle' },
      { id: 'num1-base2', x: 40, y: 83, width: 20, height: 12, shape: 'rectangle' },
      { id: 'num1-base3', x: 60, y: 83, width: 20, height: 12, shape: 'rectangle' },
    ]
  },

  // Circular Pattern Template
  {
    id: 'circle-pattern',
    name: 'Circle Pattern',
    description: 'Circular arrangement of photos',
    category: 'shape',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 800,
    backgroundColor: '#f5f5f5',
    slots: [
      // Center circle
      { id: 'center', x: 37.5, y: 37.5, width: 25, height: 25, shape: 'circle' },
      
      // Inner ring (8 photos)
      { id: 'inner-1', x: 50, y: 15, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-2', x: 70, y: 25, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-3', x: 80, y: 50, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-4', x: 70, y: 75, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-5', x: 50, y: 85, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-6', x: 25, y: 75, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-7', x: 15, y: 50, width: 15, height: 15, shape: 'circle' },
      { id: 'inner-8', x: 25, y: 25, width: 15, height: 15, shape: 'circle' },
      
      // Outer ring (4 photos)
      { id: 'outer-1', x: 50, y: 2, width: 12, height: 12, shape: 'circle' },
      { id: 'outer-2', x: 88, y: 50, width: 12, height: 12, shape: 'circle' },
      { id: 'outer-3', x: 50, y: 88, width: 12, height: 12, shape: 'circle' },
      { id: 'outer-4', x: 2, y: 50, width: 12, height: 12, shape: 'circle' },
    ]
  },

  // Birthday Templates
  {
    id: 'birthday-cake',
    name: 'Birthday Cake',
    description: 'Celebrate birthdays with cake-shaped collage',
    category: 'birthday',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 700,
    backgroundColor: '#f0f8ff',
    slots: [
      { id: 'cake-1', x: 20, y: 60, width: 60, height: 25, shape: 'rectangle' },
      { id: 'cake-2', x: 25, y: 40, width: 50, height: 20, shape: 'rectangle' },
      { id: 'cake-3', x: 35, y: 25, width: 30, height: 15, shape: 'rectangle' },
      { id: 'cake-4', x: 45, y: 10, width: 10, height: 15, shape: 'rectangle' },
    ]
  },
  {
    id: 'birthday-balloons',
    name: 'Birthday Balloons',
    description: 'Fun balloon-shaped photo arrangement',
    category: 'birthday',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 700,
    backgroundColor: '#f0f8ff',
    slots: [
      { id: 'balloon-1', x: 20, y: 10, width: 25, height: 30, shape: 'circle' },
      { id: 'balloon-2', x: 50, y: 15, width: 25, height: 30, shape: 'circle' },
      { id: 'balloon-3', x: 75, y: 25, width: 20, height: 25, shape: 'circle' },
      { id: 'balloon-4', x: 30, y: 45, width: 40, height: 40, shape: 'rectangle' },
    ]
  },

  // Family Templates
  {
    id: 'family-tree',
    name: 'Family Tree',
    description: 'Beautiful family tree photo arrangement',
    category: 'family',
    thumbnail: '',
    canvasWidth: 700,
    canvasHeight: 800,
    backgroundColor: '#f5f5dc',
    slots: [
      { id: 'grandparents-1', x: 20, y: 10, width: 25, height: 25, shape: 'circle' },
      { id: 'grandparents-2', x: 55, y: 10, width: 25, height: 25, shape: 'circle' },
      { id: 'parents-1', x: 15, y: 45, width: 30, height: 30, shape: 'circle' },
      { id: 'parents-2', x: 55, y: 45, width: 30, height: 30, shape: 'circle' },
      { id: 'kids-1', x: 10, y: 80, width: 20, height: 20, shape: 'circle' },
      { id: 'kids-2', x: 35, y: 80, width: 20, height: 20, shape: 'circle' },
      { id: 'kids-3', x: 60, y: 80, width: 20, height: 20, shape: 'circle' },
    ]
  },
  {
    id: 'mom-collage',
    name: 'Mom Collage',
    description: 'Special collage dedicated to mom',
    category: 'family',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 600,
    backgroundColor: '#fff0f5',
    slots: [
      { id: 'mom-1', x: 25, y: 10, width: 50, height: 40, shape: 'rectangle' },
      { id: 'mom-2', x: 10, y: 55, width: 25, height: 25, shape: 'circle' },
      { id: 'mom-3', x: 40, y: 55, width: 25, height: 25, shape: 'circle' },
      { id: 'mom-4', x: 70, y: 55, width: 25, height: 25, shape: 'circle' },
    ]
  },

  // Wedding Templates
  {
    id: 'wedding-rings',
    name: 'Wedding Rings',
    description: 'Beautiful wedding ring-shaped collage',
    category: 'wedding',
    thumbnail: '',
    canvasWidth: 700,
    canvasHeight: 500,
    backgroundColor: '#fff8dc',
    slots: [
      { id: 'ring-1', x: 20, y: 25, width: 25, height: 25, shape: 'circle' },
      { id: 'ring-2', x: 55, y: 25, width: 25, height: 25, shape: 'circle' },
      { id: 'center', x: 35, y: 60, width: 30, height: 30, shape: 'heart' },
    ]
  },

  // Travel Templates
  {
    id: 'travel-map',
    name: 'Travel Map',
    description: 'Document your travels with this map-style collage',
    category: 'travel',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 600,
    backgroundColor: '#e6f3ff',
    slots: [
      { id: 'destination-1', x: 10, y: 10, width: 25, height: 25, shape: 'circle' },
      { id: 'destination-2', x: 40, y: 20, width: 25, height: 25, shape: 'circle' },
      { id: 'destination-3', x: 70, y: 15, width: 25, height: 25, shape: 'circle' },
      { id: 'main-photo', x: 25, y: 50, width: 50, height: 40, shape: 'rectangle' },
    ]
  },

  // Memory Templates
  {
    id: 'memory-lane',
    name: 'Memory Lane',
    description: 'Cherish your memories with this timeline collage',
    category: 'memory',
    thumbnail: '',
    canvasWidth: 800,
    canvasHeight: 400,
    backgroundColor: '#f0f0f0',
    slots: [
      { id: 'memory-1', x: 5, y: 25, width: 15, height: 20, shape: 'rectangle' },
      { id: 'memory-2', x: 25, y: 25, width: 15, height: 20, shape: 'rectangle' },
      { id: 'memory-3', x: 45, y: 25, width: 15, height: 20, shape: 'rectangle' },
      { id: 'memory-4', x: 65, y: 25, width: 15, height: 20, shape: 'rectangle' },
      { id: 'memory-5', x: 85, y: 25, width: 15, height: 20, shape: 'rectangle' },
    ]
  },

  // Friends Templates
  {
    id: 'best-friends',
    name: 'Best Friends',
    description: 'Celebrate friendship with this special collage',
    category: 'friends',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 600,
    backgroundColor: '#fff8dc',
    slots: [
      { id: 'friend-1', x: 15, y: 15, width: 30, height: 30, shape: 'circle' },
      { id: 'friend-2', x: 55, y: 15, width: 30, height: 30, shape: 'circle' },
      { id: 'group', x: 25, y: 55, width: 50, height: 35, shape: 'rectangle' },
    ]
  },

  // Anniversary Templates
  {
    id: 'anniversary-25th',
    name: '25th Anniversary',
    description: 'Silver anniversary celebration collage',
    category: 'anniversary',
    thumbnail: '',
    canvasWidth: 600,
    canvasHeight: 600,
    backgroundColor: '#f5f5f5',
    slots: [
      { id: 'then', x: 10, y: 20, width: 35, height: 30, shape: 'rectangle' },
      { id: 'now', x: 55, y: 20, width: 35, height: 30, shape: 'rectangle' },
      { id: 'celebration', x: 25, y: 60, width: 50, height: 30, shape: 'rectangle' },
    ]
  }
];

export const getTemplateById = (id: string): CollageTemplate | undefined => {
  return templates.find(template => template.id === id);
};

export const getTemplatesByCategory = (category: CollageTemplate['category']): CollageTemplate[] => {
  return templates.filter(template => template.category === category);
};
