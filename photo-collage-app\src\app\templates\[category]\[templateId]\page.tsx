import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { templates } from '@/data/templates';
import SimpleEditor from '@/components/SimpleEditor';
import { Heart, Grid, Type, Hash, Circle } from 'lucide-react';

interface PageProps {
  params: {
    category: string;
    templateId: string;
  };
}

const categoryIcons = {
  grid: Grid,
  heart: Heart,
  letter: Type,
  number: Hash,
  shape: Circle
};

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { category, templateId } = params;
  const template = templates.find(t => t.id === templateId && t.category === category);
  
  if (!template) {
    return {
      title: 'Template Not Found',
      description: 'The requested template was not found.'
    };
  }

  const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
  const title = `${template.name} - ${categoryName} Photo Collage Template`;
  const description = `Create beautiful ${template.name.toLowerCase()} photo collages with our free online editor. ${template.description}. Perfect for social media, gifts, and photo displays.`;

  return {
    title,
    description,
    keywords: [
      `${template.name.toLowerCase()} photo collage`,
      `${category} photo collage`,
      `${template.name.toLowerCase()} template`,
      'free photo collage maker',
      'online collage editor',
      `${category} collage template`
    ],
    openGraph: {
      title,
      description,
      type: 'website',
      url: `https://photocollagemakerpro.com/templates/${category}/${templateId}`,
      images: [
        {
          url: `https://photocollagemakerpro.com/templates/${templateId}-preview.jpg`,
          width: 1200,
          height: 630,
          alt: `${template.name} Photo Collage Template`
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`https://photocollagemakerpro.com/templates/${templateId}-preview.jpg`]
    },
    alternates: {
      canonical: `https://photocollagemakerpro.com/templates/${category}/${templateId}`
    }
  };
}

export async function generateStaticParams() {
  const params: { category: string; templateId: string }[] = [];
  
  templates.forEach(template => {
    params.push({
      category: template.category,
      templateId: template.id
    });
  });
  
  return params;
}

export default function TemplatePage({ params }: PageProps) {
  const { category, templateId } = params;
  const template = templates.find(t => t.id === templateId && t.category === category);
  
  if (!template) {
    notFound();
  }

  const IconComponent = categoryIcons[category as keyof typeof categoryIcons];
  const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
  
  // Related templates from the same category
  const relatedTemplates = templates
    .filter(t => t.category === category && t.id !== templateId)
    .slice(0, 6);

  // Structured data for the template page
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'CreativeWork',
    name: `${template.name} Photo Collage Template`,
    description: template.description,
    category: template.category,
    creator: {
      '@type': 'Organization',
      name: 'Photo Collage Maker Pro'
    },
    url: `https://photocollagemakerpro.com/templates/${category}/${templateId}`,
    image: `https://photocollagemakerpro.com/templates/${templateId}-preview.jpg`,
    dateCreated: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    license: 'https://creativecommons.org/licenses/by/4.0/',
    usageInfo: 'Free for personal and commercial use',
    keywords: [
      `${template.name.toLowerCase()} photo collage`,
      `${category} photo collage`,
      'free photo collage template',
      'online collage maker'
    ],
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: 'https://photocollagemakerpro.com'
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Templates',
          item: 'https://photocollagemakerpro.com/templates'
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: `${categoryName} Templates`,
          item: `https://photocollagemakerpro.com/templates/${category}`
        },
        {
          '@type': 'ListItem',
          position: 4,
          name: template.name,
          item: `https://photocollagemakerpro.com/templates/${category}/${templateId}`
        }
      ]
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            {IconComponent && <IconComponent className="w-10 h-10 text-blue-600 mr-3" />}
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800">
              {template.name} Photo Collage Template
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {template.description}. Create your personalized {template.name.toLowerCase()} collage with {template.slots.length} photo slots.
          </p>
        </div>

        {/* Template Editor */}
        <SimpleEditor initialTemplateId={templateId} />
        
        {/* Template Details */}
        <div className="mt-12 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-2xl font-bold mb-6">Template Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">Template Information</h3>
                <ul className="space-y-2 text-gray-600">
                  <li><strong>Name:</strong> {template.name}</li>
                  <li><strong>Category:</strong> {categoryName}</li>
                  <li><strong>Photo Slots:</strong> {template.slots.length}</li>
                  <li><strong>Canvas Size:</strong> {template.canvasWidth} × {template.canvasHeight}px</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Perfect For</h3>
                <ul className="space-y-1 text-gray-600">
                  <li>• Social media posts</li>
                  <li>• Photo gifts and prints</li>
                  <li>• Memory books and albums</li>
                  <li>• Digital photo displays</li>
                  <li>• Special occasion cards</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Related Templates */}
        {relatedTemplates.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-center mb-8">
              More {categoryName} Templates
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {relatedTemplates.map((relatedTemplate) => (
                <a
                  key={relatedTemplate.id}
                  href={`/templates/${category}/${relatedTemplate.id}`}
                  className="bg-white rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="text-center">
                    <div className="w-full h-20 bg-gray-100 rounded mb-2 flex items-center justify-center">
                      <span className="text-xs text-gray-500">{relatedTemplate.name}</span>
                    </div>
                    <p className="text-sm font-medium">{relatedTemplate.name}</p>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}

        {/* SEO Content */}
        <div className="mt-16 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-2xl font-bold mb-6">
              How to Use the {template.name} Template
            </h2>
            <div className="prose max-w-none">
              <p className="mb-4">
                The {template.name} template is perfect for creating {category} photo collages with {template.slots.length} photos. 
                This template offers a {template.canvasWidth} × {template.canvasHeight} pixel canvas for high-quality results.
              </p>
              <ol className="list-decimal list-inside space-y-3">
                <li><strong>Upload Your Photos:</strong> Select {template.slots.length} photos that you want to include in your {template.name.toLowerCase()} collage</li>
                <li><strong>Automatic Placement:</strong> Photos will be automatically placed in the template slots</li>
                <li><strong>Customize Layout:</strong> Drag photos between slots to rearrange them as desired</li>
                <li><strong>Adjust Photos:</strong> Click on any photo to adjust its position, scale, and rotation</li>
                <li><strong>Download Result:</strong> Save your finished {template.name.toLowerCase()} collage as a high-quality PNG</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
