import { Metadata } from 'next';
import { Card, CardContent } from '@/components/ui/card';

export const metadata: Metadata = {
  title: 'FAQ - Photo Collage Maker | Frequently Asked Questions',
  description: 'Find answers to common questions about our free photo collage maker. Learn how to create collages, download options, supported formats, and more.',
  keywords: [
    'photo collage FAQ',
    'collage maker help',
    'photo collage questions',
    'how to make collage',
    'collage maker support'
  ],
  alternates: {
    canonical: 'https://photocollagemakerpro.com/faq'
  }
};

const faqs = [
  {
    question: "Is the photo collage maker really free?",
    answer: "Yes! Our photo collage maker is completely free to use. There are no hidden fees, no watermarks on your downloads, and no registration required. You can create and download unlimited collages at no cost."
  },
  {
    question: "What image formats are supported?",
    answer: "We support all common image formats including JPG, JPEG, PNG, and WebP. For best results, we recommend using high-resolution images (at least 1000px on the longest side)."
  },
  {
    question: "How many photos can I add to a collage?",
    answer: "The number of photos depends on the template you choose. Our templates range from 2 photos up to 16 photos. Each template clearly shows how many photo slots are available."
  },
  {
    question: "What quality are the downloaded collages?",
    answer: "All collages are downloaded as high-quality PNG files. The resolution depends on the template size, with most templates producing images suitable for both web use and printing."
  },
  {
    question: "Can I use the collages commercially?",
    answer: "Yes, you can use the collages you create for both personal and commercial purposes. However, make sure you have the rights to use all the photos you include in your collage."
  },
  {
    question: "Do I need to create an account?",
    answer: "No account creation is required. You can start creating collages immediately without any registration or sign-up process."
  },
  {
    question: "Can I edit my collage after creating it?",
    answer: "Currently, you can only edit your collage during the creation session. Once you leave the page, you'll need to start over. We recommend saving your work by downloading it before leaving."
  },
  {
    question: "Are there size limits for uploaded photos?",
    answer: "We recommend keeping individual photos under 10MB for optimal performance. Very large files may take longer to upload and process."
  },
  {
    question: "Can I create custom templates?",
    answer: "Currently, we offer 50+ pre-designed templates. Custom template creation is not available, but we regularly add new templates based on user feedback."
  },
  {
    question: "Is my data safe and private?",
    answer: "Yes, your privacy is important to us. Photos are processed locally in your browser when possible, and we don't store your images on our servers permanently."
  },
  {
    question: "What browsers are supported?",
    answer: "Our collage maker works on all modern browsers including Chrome, Firefox, Safari, and Edge. For the best experience, we recommend using the latest version of your browser."
  },
  {
    question: "Can I use this on mobile devices?",
    answer: "Yes! Our collage maker is mobile-friendly and works on smartphones and tablets. The interface adapts to smaller screens for easy use on any device."
  }
];

export default function FAQPage() {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to common questions about our free photo collage maker. 
            Can't find what you're looking for? Contact us for help!
          </p>
        </div>

        {/* FAQ Grid */}
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-6">
            {faqs.map((faq, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold text-gray-800 mb-3">
                    {faq.question}
                  </h2>
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-lg p-8 shadow-sm max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Ready to Create Your Collage?</h2>
            <p className="text-gray-600 mb-6">
              Now that you know how easy it is, why not give it a try? 
              Start creating beautiful photo collages in minutes!
            </p>
            <a
              href="/"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Start Creating Now
            </a>
          </div>
        </div>

        {/* Additional Help */}
        <div className="mt-12 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-2xl font-bold mb-6">Still Need Help?</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-3">Quick Tips</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• Use high-resolution photos for best results</li>
                  <li>• Try different templates to find your perfect style</li>
                  <li>• Arrange photos before fine-tuning positions</li>
                  <li>• Download your collage as soon as you're happy with it</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-3">Popular Templates</h3>
                <ul className="space-y-2 text-gray-600">
                  <li>• <a href="/templates/grid" className="text-blue-600 hover:underline">Grid layouts</a> for organized displays</li>
                  <li>• <a href="/templates/heart" className="text-blue-600 hover:underline">Heart shapes</a> for romantic occasions</li>
                  <li>• <a href="/templates/letter" className="text-blue-600 hover:underline">Letter templates</a> for personalized messages</li>
                  <li>• <a href="/templates/number" className="text-blue-600 hover:underline">Number shapes</a> for celebrations</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
