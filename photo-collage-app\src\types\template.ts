export interface PhotoSlot {
  id: string;
  x: number; // X position as percentage (0-100)
  y: number; // Y position as percentage (0-100)
  width: number; // Width as percentage (0-100)
  height: number; // Height as percentage (0-100)
  rotation?: number; // Rotation in degrees
  shape?: 'rectangle' | 'circle' | 'custom'; // Shape of the slot
  maskPath?: string; // SVG path for custom shapes
}

export interface CollageTemplate {
  id: string;
  name: string;
  description: string;
  category: 'letter' | 'number' | 'shape' | 'grid' | 'heart' | 'birthday' | 'family' | 'wedding' | 'travel' | 'memory' | 'friends' | 'anniversary';
  thumbnail: string; // Base64 or URL to preview image
  slots: PhotoSlot[];
  canvasWidth: number; // Template canvas width in pixels
  canvasHeight: number; // Template canvas height in pixels
  backgroundColor?: string;
  backgroundPattern?: string;
}

export interface UploadedPhoto {
  id: string;
  file: File;
  url: string; // Object URL for preview
  width: number;
  height: number;
}

export interface PlacedPhoto {
  photoId: string;
  slotId: string;
  x: number; // Position within slot (0-100%)
  y: number; // Position within slot (0-100%)
  scale: number; // Scale factor (0.1 - 3.0)
  rotation: number; // Rotation in degrees
}
