import { MetadataRoute } from 'next';
import { templates } from '@/data/templates';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://photocollagemakerpro.com';
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 1,
    },
  ];

  // Category pages
  const categories = [...new Set(templates.map(template => template.category))];
  const categoryPages = categories.map(category => ({
    url: `${baseUrl}/templates/${category}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));

  // Individual template pages
  const templatePages = templates.map(template => ({
    url: `${baseUrl}/templates/${template.category}/${template.id}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.6,
  }));

  return [...staticPages, ...categoryPages, ...templatePages];
}
