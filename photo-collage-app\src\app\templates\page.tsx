import { Metadata } from 'next';
import Link from 'next/link';
import { templates } from '@/data/templates';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Heart, Grid, Type, Hash, Circle } from 'lucide-react';

const categoryIcons = {
  grid: Grid,
  heart: Heart,
  letter: Type,
  number: Hash,
  shape: Circle
};

const categoryDescriptions = {
  grid: 'Perfect grid layouts for organized photo displays',
  heart: 'Romantic heart-shaped collages for special occasions',
  letter: 'Spell out names and messages with letter-shaped layouts',
  number: 'Celebrate milestones with number-shaped collages',
  shape: 'Creative geometric shapes for artistic arrangements'
};

export const metadata: Metadata = {
  title: 'Photo Collage Templates - Free Templates for Every Occasion',
  description: 'Browse our collection of 50+ free photo collage templates. Choose from heart shapes, grids, letters, numbers, and creative layouts. Perfect for social media, gifts, and memories.',
  keywords: [
    'photo collage templates',
    'free collage templates',
    'collage layouts',
    'photo template gallery',
    'heart collage templates',
    'grid collage templates',
    'letter collage templates',
    'number collage templates'
  ],
  openGraph: {
    title: 'Photo Collage Templates - Free Templates for Every Occasion',
    description: 'Browse our collection of 50+ free photo collage templates. Choose from heart shapes, grids, letters, numbers, and creative layouts.',
    url: 'https://photocollagemakerpro.com/templates',
    images: [
      {
        url: 'https://photocollagemakerpro.com/templates-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Photo Collage Templates Gallery'
      }
    ]
  },
  alternates: {
    canonical: 'https://photocollagemakerpro.com/templates'
  }
};

export default function TemplatesPage() {
  // Group templates by category
  const templatesByCategory = templates.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, typeof templates>);

  // Structured data for the templates page
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: 'Photo Collage Templates',
    description: 'Browse our collection of 50+ free photo collage templates. Choose from heart shapes, grids, letters, numbers, and creative layouts.',
    url: 'https://photocollagemakerpro.com/templates',
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: templates.length,
      itemListElement: Object.entries(templatesByCategory).map(([category, categoryTemplates], index) => ({
        '@type': 'CreativeWork',
        position: index + 1,
        name: `${category.charAt(0).toUpperCase() + category.slice(1)} Templates`,
        description: categoryDescriptions[category as keyof typeof categoryDescriptions],
        category: category,
        url: `https://photocollagemakerpro.com/templates/${category}`,
        numberOfItems: categoryTemplates.length
      }))
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: 'https://photocollagemakerpro.com'
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Templates',
          item: 'https://photocollagemakerpro.com/templates'
        }
      ]
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
            Photo Collage Templates
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Browse our collection of {templates.length}+ free photo collage templates. 
            Choose from heart shapes, grids, letters, numbers, and creative layouts perfect for any occasion.
          </p>
        </div>

        {/* Template Categories */}
        <div className="space-y-12">
          {Object.entries(templatesByCategory).map(([category, categoryTemplates]) => {
            const IconComponent = categoryIcons[category as keyof typeof categoryIcons];
            const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
            
            return (
              <section key={category} className="mb-12">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    {IconComponent && <IconComponent className="w-8 h-8 text-blue-600 mr-3" />}
                    <h2 className="text-3xl font-bold text-gray-800">
                      {categoryName} Templates
                    </h2>
                    <Badge variant="secondary" className="ml-3">
                      {categoryTemplates.length} templates
                    </Badge>
                  </div>
                  <Link 
                    href={`/templates/${category}`}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View All →
                  </Link>
                </div>
                
                <p className="text-gray-600 mb-6">
                  {categoryDescriptions[category as keyof typeof categoryDescriptions]}
                </p>

                {/* Template Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {categoryTemplates.slice(0, 6).map((template) => (
                    <Link
                      key={template.id}
                      href={`/templates/${category}/${template.id}`}
                      className="group"
                    >
                      <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardContent className="p-3">
                          <div className="aspect-square bg-gray-100 rounded mb-2 flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                            <span className="text-xs text-gray-500 text-center px-1">
                              {template.name}
                            </span>
                          </div>
                          <h3 className="text-sm font-medium text-center truncate">
                            {template.name}
                          </h3>
                          <p className="text-xs text-gray-500 text-center mt-1">
                            {template.slots.length} photos
                          </p>
                        </CardContent>
                      </Card>
                    </Link>
                  ))}
                </div>

                {categoryTemplates.length > 6 && (
                  <div className="text-center mt-6">
                    <Link
                      href={`/templates/${category}`}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      View All {categoryTemplates.length} {categoryName} Templates
                    </Link>
                  </div>
                )}
              </section>
            );
          })}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-lg p-8 shadow-sm max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">Ready to Create Your Collage?</h2>
            <p className="text-gray-600 mb-6">
              Choose any template above to start creating your personalized photo collage. 
              It's free, easy, and takes just minutes!
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Start Creating Now
            </Link>
          </div>
        </div>

        {/* SEO Content */}
        <div className="mt-16 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-2xl font-bold mb-6">About Our Photo Collage Templates</h2>
            <div className="prose max-w-none">
              <p className="mb-4">
                Our collection of {templates.length}+ free photo collage templates offers something for every occasion and style. 
                Whether you're creating a romantic heart collage for Valentine's Day, organizing family photos in a grid layout, 
                or spelling out a special message with letter templates, we have the perfect design for you.
              </p>
              <h3 className="text-xl font-semibold mb-3">Template Categories</h3>
              <ul className="list-disc list-inside space-y-2 mb-4">
                <li><strong>Grid Templates:</strong> Perfect for organizing multiple photos in clean, symmetrical layouts</li>
                <li><strong>Heart Templates:</strong> Romantic designs ideal for anniversaries, weddings, and Valentine's Day</li>
                <li><strong>Letter Templates:</strong> Spell out names, initials, or messages with photo-filled letters</li>
                <li><strong>Number Templates:</strong> Celebrate birthdays, anniversaries, and milestones with number shapes</li>
                <li><strong>Shape Templates:</strong> Creative geometric and artistic layouts for unique presentations</li>
              </ul>
              <p>
                All templates are completely free to use and require no registration. Simply choose your favorite design, 
                upload your photos, and download your finished collage in high quality.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
